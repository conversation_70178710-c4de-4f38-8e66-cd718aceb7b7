import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

/// خدمة Firebase الرئيسية لإدارة جميع عمليات Firebase
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  // Getters
  FirebaseAuth get auth => _auth;
  FirebaseFirestore get firestore => _firestore;
  FirebaseStorage get storage => _storage;
  FirebaseMessaging get messaging => _messaging;
  FirebaseAnalytics get analytics => _analytics;
  FirebaseCrashlytics get crashlytics => _crashlytics;

  /// تهيئة خدمات Firebase
  Future<void> initialize() async {
    try {
      // تهيئة Firebase Messaging
      await _initializeMessaging();
      
      // تهيئة Firebase Analytics
      await _initializeAnalytics();
      
      // تهيئة Firebase Crashlytics
      await _initializeCrashlytics();
      
      debugPrint('تم تهيئة خدمات Firebase بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات Firebase: $e');
      _crashlytics.recordError(e, null);
    }
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeMessaging() async {
    try {
      // طلب إذن الإشعارات
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('تم منح إذن الإشعارات');
        
        // الحصول على FCM token
        String? token = await _messaging.getToken();
        debugPrint('FCM Token: $token');
        
        // الاستماع لتغييرات الـ token
        _messaging.onTokenRefresh.listen((newToken) {
          debugPrint('FCM Token تم تحديثه: $newToken');
          // يمكن حفظ الـ token الجديد في قاعدة البيانات
        });
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة Firebase Messaging: $e');
    }
  }

  /// تهيئة Firebase Analytics
  Future<void> _initializeAnalytics() async {
    try {
      await _analytics.setAnalyticsCollectionEnabled(true);
      debugPrint('تم تفعيل Firebase Analytics');
    } catch (e) {
      debugPrint('خطأ في تهيئة Firebase Analytics: $e');
    }
  }

  /// تهيئة Firebase Crashlytics
  Future<void> _initializeCrashlytics() async {
    try {
      await _crashlytics.setCrashlyticsCollectionEnabled(true);
      debugPrint('تم تفعيل Firebase Crashlytics');
    } catch (e) {
      debugPrint('خطأ في تهيئة Firebase Crashlytics: $e');
    }
  }

  /// تسجيل حدث في Analytics
  Future<void> logEvent(String name, Map<String, Object>? parameters) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      debugPrint('خطأ في تسجيل الحدث: $e');
    }
  }

  /// تسجيل خطأ في Crashlytics
  void recordError(dynamic exception, StackTrace? stackTrace, {String? reason}) {
    try {
      _crashlytics.recordError(exception, stackTrace, reason: reason);
    } catch (e) {
      debugPrint('خطأ في تسجيل الخطأ: $e');
    }
  }

  /// تسجيل رسالة مخصصة في Crashlytics
  void log(String message) {
    try {
      _crashlytics.log(message);
    } catch (e) {
      debugPrint('خطأ في تسجيل الرسالة: $e');
    }
  }

  /// تعيين معرف المستخدم
  Future<void> setUserId(String userId) async {
    try {
      await _analytics.setUserId(id: userId);
      await _crashlytics.setUserIdentifier(userId);
    } catch (e) {
      debugPrint('خطأ في تعيين معرف المستخدم: $e');
    }
  }

  /// تعيين خصائص المستخدم
  Future<void> setUserProperty(String name, String value) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
    } catch (e) {
      debugPrint('خطأ في تعيين خاصية المستخدم: $e');
    }
  }

  /// الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  /// التحقق من حالة تسجيل الدخول
  bool get isSignedIn => _auth.currentUser != null;

  /// الاستماع لتغييرات حالة المصادقة
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential?> signInWithEmailAndPassword(String email, String password) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      await logEvent('login', {'method': 'email'});
      return result;
    } catch (e) {
      recordError(e, null, reason: 'Sign in with email failed');
      rethrow;
    }
  }

  /// إنشاء حساب جديد
  Future<UserCredential?> createUserWithEmailAndPassword(String email, String password) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      await logEvent('sign_up', {'method': 'email'});
      return result;
    } catch (e) {
      recordError(e, null, reason: 'Create user failed');
      rethrow;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await logEvent('logout', null);
    } catch (e) {
      recordError(e, null, reason: 'Sign out failed');
      rethrow;
    }
  }

  /// إرسال رسالة إعادة تعيين كلمة المرور
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      await logEvent('password_reset_sent', {'email': email});
    } catch (e) {
      recordError(e, null, reason: 'Password reset failed');
      rethrow;
    }
  }

  /// حفظ بيانات في Firestore
  Future<void> saveDocument(String collection, String docId, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(docId).set(data);
    } catch (e) {
      recordError(e, null, reason: 'Save document failed');
      rethrow;
    }
  }

  /// الحصول على مستند من Firestore
  Future<DocumentSnapshot> getDocument(String collection, String docId) async {
    try {
      return await _firestore.collection(collection).doc(docId).get();
    } catch (e) {
      recordError(e, null, reason: 'Get document failed');
      rethrow;
    }
  }

  /// الحصول على مجموعة من Firestore
  Future<QuerySnapshot> getCollection(String collection) async {
    try {
      return await _firestore.collection(collection).get();
    } catch (e) {
      recordError(e, null, reason: 'Get collection failed');
      rethrow;
    }
  }

  /// رفع ملف إلى Firebase Storage
  Future<String> uploadFile(String path, List<int> data) async {
    try {
      Reference ref = _storage.ref().child(path);
      UploadTask uploadTask = ref.putData(Uint8List.fromList(data));
      TaskSnapshot snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      recordError(e, null, reason: 'Upload file failed');
      rethrow;
    }
  }

  /// حذف ملف من Firebase Storage
  Future<void> deleteFile(String path) async {
    try {
      Reference ref = _storage.ref().child(path);
      await ref.delete();
    } catch (e) {
      recordError(e, null, reason: 'Delete file failed');
      rethrow;
    }
  }
}
