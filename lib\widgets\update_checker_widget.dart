import 'package:flutter/material.dart';
import '../services/app_update_service.dart';

/// Widget للتحقق من التحديثات يمكن استخدامه في أي مكان
class UpdateCheckerWidget extends StatelessWidget {
  final Widget child;
  final bool checkOnInit;

  const UpdateCheckerWidget({
    super.key,
    required this.child,
    this.checkOnInit = true,
  });

  @override
  Widget build(BuildContext context) {
    // التحقق من التحديثات عند بناء الـ widget
    if (checkOnInit) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkForUpdates(context);
      });
    }

    return child;
  }

  /// التحقق من التحديثات
  Future<void> _checkForUpdates(BuildContext context) async {
    try {
      await AppUpdateService().checkForUpdate(context);
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');
    }
  }
}

/// زر للتحقق اليدوي من التحديثات
class ManualUpdateButton extends StatefulWidget {
  final String? text;
  final IconData? icon;
  final Color? color;

  const ManualUpdateButton({
    super.key,
    this.text,
    this.icon,
    this.color,
  });

  @override
  State<ManualUpdateButton> createState() => _ManualUpdateButtonState();
}

class _ManualUpdateButtonState extends State<ManualUpdateButton> {
  bool _isChecking = false;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: _isChecking ? null : _checkForUpdates,
      icon: _isChecking 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(widget.icon ?? Icons.system_update),
      label: Text(
        _isChecking 
            ? 'جاري التحقق...' 
            : (widget.text ?? 'التحقق من التحديثات'),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.color ?? Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Future<void> _checkForUpdates() async {
    if (_isChecking) return;

    setState(() {
      _isChecking = true;
    });

    try {
      await AppUpdateService().checkForUpdate(context);
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في التحقق من التحديثات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChecking = false;
        });
      }
    }
  }
}

/// مثال على كيفية الاستخدام في أي شاشة
class ExampleUsageScreen extends StatelessWidget {
  const ExampleUsageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return UpdateCheckerWidget(
      checkOnInit: true, // التحقق التلقائي عند فتح الشاشة
      child: Scaffold(
        appBar: AppBar(
          title: const Text('مثال على الاستخدام'),
          actions: [
            // زر للتحقق اليدوي من التحديثات
            IconButton(
              onPressed: () async {
                await AppUpdateService().checkForUpdate(context);
              },
              icon: const Icon(Icons.system_update),
              tooltip: 'التحقق من التحديثات',
            ),
          ],
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'مثال على استخدام نظام التحديث',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              ManualUpdateButton(
                text: 'تحقق من التحديثات الآن',
                icon: Icons.download,
                color: Colors.green,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
