# 📱 دليل تحديث الإصدار - EduTrack

## 🔄 كيفية إصدار نسخة جديدة من التطبيق:

### الخطوة 1: تحديث رقم الإصدار في التطبيق
في ملف `pubspec.yaml`، حدث رقم الإصدار:

```yaml
# الإصدار الحالي
version: 1.0.0+1

# الإصدار الجديد (مثال)
version: 1.0.1+2
```

**ملاحظة:** 
- الرقم الأول (1.0.1) هو رقم الإصدار المرئي للمستخدم
- الرقم الثاني (+2) هو رقم البناء (Build Number)

### الخطوة 2: بناء ملف APK
```bash
# في مجلد المشروع
flutter build apk --release
```

الملف سيكون في: `build/app/outputs/flutter-apk/app-release.apk`

### الخطوة 3: رفع ملف APK
ارفع الملف إلى:
- **Firebase Storage** (الأفضل)
- **Google Drive** 
- **Dropbox**
- **خادم خاص**

احصل على الرابط المباشر للتحميل.

### الخطوة 4: تحديث Firebase Remote Config

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `edu-track-95f66`
3. اذهب إلى **Remote Config**
4. حدث المتغيرات:

```json
{
  "latest_version": "1.0.1",  // رقم الإصدار الجديد
  "apk_url": "https://firebasestorage.googleapis.com/v0/b/edu-track-95f66.appspot.com/o/releases%2Fapp-release-v1.0.1.apk?alt=media&token=your-token"
}
```

5. انقر **Publish changes**

## 🎯 مثال عملي:

### إصدار 1.0.0 → 1.0.1:

#### في pubspec.yaml:
```yaml
# من
version: 1.0.0+1

# إلى  
version: 1.0.1+2
```

#### في Firebase Remote Config:
```json
{
  "latest_version": "1.0.1",
  "apk_url": "https://your-server.com/app-release-v1.0.1.apk"
}
```

## 📋 قائمة التحقق السريعة:

- [ ] تحديث `version` في pubspec.yaml
- [ ] تشغيل `flutter build apk --release`
- [ ] رفع ملف APK إلى الخادم
- [ ] تحديث `latest_version` في Firebase Remote Config
- [ ] تحديث `apk_url` في Firebase Remote Config
- [ ] نشر التغييرات في Firebase Console

## 🔍 اختبار التحديث:

1. **تثبيت الإصدار القديم** على جهاز اختبار
2. **تحديث Firebase Remote Config** بالإصدار الجديد
3. **فتح التطبيق** - يجب أن يظهر تنبيه التحديث
4. **اختبار التحميل والتثبيت**

## ⚠️ ملاحظات مهمة:

### أرقام الإصدارات:
- **1.0.0** → **1.0.1**: إصلاح أخطاء صغيرة
- **1.0.0** → **1.1.0**: ميزات جديدة صغيرة  
- **1.0.0** → **2.0.0**: تغييرات كبيرة

### الأمان:
- تأكد من أن ملف APK موقع رقمياً
- استخدم HTTPS للروابط
- احتفظ بنسخة احتياطية من الإصدارات السابقة

### Firebase Storage (الطريقة المفضلة):

#### رفع الملف:
1. اذهب إلى Firebase Console > Storage
2. ارفع ملف APK إلى مجلد `releases/`
3. انقر على الملف واختر "Get download URL"
4. انسخ الرابط واستخدمه في Remote Config

#### مثال على الرابط:
```
https://firebasestorage.googleapis.com/v0/b/edu-track-95f66.appspot.com/o/releases%2Fapp-release-v1.0.1.apk?alt=media&token=abc123
```

## 🚀 النظام جاهز!

نظام التحديث التلقائي يعمل الآن بالكامل:
- ✅ يتحقق تلقائياً عند بدء التطبيق
- ✅ يعرض تنبيه عربي للمستخدم
- ✅ يحمل ويثبت التحديث تلقائياً
- ✅ يعمل فقط عند وجود اتصال إنترنت

كل ما عليك فعله هو اتباع الخطوات أعلاه عند إصدار نسخة جديدة! 🎉
