import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_update_service.dart';
import 'intro_screen.dart';
import 'splash_screen.dart';

class StartupScreen extends StatefulWidget {
  const StartupScreen({super.key});

  @override
  State<StartupScreen> createState() => _StartupScreenState();
}

class _StartupScreenState extends State<StartupScreen> {
  @override
  void initState() {
    super.initState();
    _checkFirstRun();
  }

  Future<void> _checkFirstRun() async {
    try {
      // التحقق من التحديثات أولاً
      await _checkForUpdates();

      final prefs = await SharedPreferences.getInstance();
      final hasSeenIntro = prefs.getBool('hasSeenIntro') ?? false;

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                hasSeenIntro ? const SplashScreen() : const IntroScreen(),
          ),
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، نعرض شاشة الترحيب افتراضيًا
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const IntroScreen()),
        );
      }
    }
  }

  /// التحقق من وجود تحديثات للتطبيق
  Future<void> _checkForUpdates() async {
    try {
      if (mounted) {
        await AppUpdateService().checkForUpdate(context);
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');
      // لا نوقف التطبيق في حالة فشل التحقق من التحديثات
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: const Center(child: CircularProgressIndicator()),
    );
  }
}
