import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التحقق من تكامل التطبيق
class IntegrityService {
  static IntegrityService? _instance;
  static IntegrityService get instance => _instance ??= IntegrityService._();
  IntegrityService._();

  static const String _integrityKey = 'app_integrity_hash';
  static const String _lastCheckKey = 'last_integrity_check';
  static const String _tamperDetectedKey = 'tamper_detected';

  /// التحقق من تكامل التطبيق
  Future<bool> verifyIntegrity() async {
    try {
      // التحقق من التوقيع الرقمي
      if (!await _verifyDigitalSignature()) {
        await _recordTamperAttempt('Digital signature verification failed');
        return false;
      }

      // التحقق من تكامل الملفات الأساسية
      if (!await _verifyFileIntegrity()) {
        await _recordTamperAttempt('File integrity check failed');
        return false;
      }

      // التحقق من البيئة
      if (!await _verifyEnvironment()) {
        await _recordTamperAttempt('Environment verification failed');
        return false;
      }

      // التحقق من أدوات التطوير
      if (!await _checkDebugTools()) {
        await _recordTamperAttempt('Debug tools detected');
        return false;
      }

      await _updateLastCheck();
      debugPrint('✅ تم التحقق من تكامل التطبيق بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التكامل: $e');
      return false;
    }
  }

  /// التحقق من وجود محاولات تلاعب
  Future<bool> hasTamperAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_tamperDetectedKey) ?? false;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من محاولات التلاعب: $e');
      return false;
    }
  }

  /// مسح سجل محاولات التلاعب
  Future<void> clearTamperRecord() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tamperDetectedKey);
    } catch (e) {
      debugPrint('❌ خطأ في مسح سجل التلاعب: $e');
    }
  }

  /// التحقق من التوقيع الرقمي
  Future<bool> _verifyDigitalSignature() async {
    try {
      // في بيئة الإنتاج، يجب التحقق من التوقيع الرقمي الفعلي
      if (kDebugMode) {
        return true; // تجاهل في وضع التطوير
      }

      // التحقق من معرف الحزمة
      // هنا يمكن إضافة التحقق الفعلي من معرف الحزمة

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التوقيع الرقمي: $e');
      return false;
    }
  }

  /// التحقق من تكامل الملفات
  Future<bool> _verifyFileIntegrity() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حساب hash للملفات الأساسية
      final currentHash = await _calculateAppHash();
      final storedHash = prefs.getString(_integrityKey);

      if (storedHash == null) {
        // أول مرة - حفظ الـ hash
        await prefs.setString(_integrityKey, currentHash);
        return true;
      }

      // مقارنة الـ hash
      if (currentHash != storedHash) {
        debugPrint('⚠️ تم اكتشاف تغيير في ملفات التطبيق');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من تكامل الملفات: $e');
      return true; // السماح بالمتابعة في حالة الخطأ
    }
  }

  /// التحقق من البيئة
  Future<bool> _verifyEnvironment() async {
    try {
      // التحقق من الجذر (Root/Jailbreak)
      if (await _isDeviceRooted()) {
        debugPrint('⚠️ تم اكتشاف جهاز مكسور الحماية');
        return false;
      }

      // التحقق من المحاكيات
      if (await _isRunningOnEmulator()) {
        debugPrint('⚠️ التطبيق يعمل على محاكي');
        // يمكن السماح بالمحاكيات في وضع التطوير
        return kDebugMode;
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البيئة: $e');
      return true;
    }
  }

  /// التحقق من أدوات التطوير
  Future<bool> _checkDebugTools() async {
    try {
      // في وضع التطوير، السماح بأدوات التطوير
      if (kDebugMode) {
        return true;
      }

      // التحقق من وجود أدوات التطوير
      // هذا مثال بسيط - يمكن تطويره أكثر
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من أدوات التطوير: $e');
      return true;
    }
  }

  /// التحقق من كسر الحماية
  Future<bool> _isDeviceRooted() async {
    try {
      if (Platform.isAndroid) {
        // فحص ملفات الجذر الشائعة
        final rootFiles = [
          '/system/app/Superuser.apk',
          '/sbin/su',
          '/system/bin/su',
          '/system/xbin/su',
          '/data/local/xbin/su',
          '/data/local/bin/su',
          '/system/sd/xbin/su',
          '/system/bin/failsafe/su',
          '/data/local/su',
        ];

        for (final file in rootFiles) {
          if (await File(file).exists()) {
            return true;
          }
        }

        // فحص تطبيقات الجذر
        try {
          final result = await Process.run('which', ['su']);
          if (result.exitCode == 0) {
            return true;
          }
        } catch (e) {
          // تجاهل الخطأ
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في فحص كسر الحماية: $e');
      return false;
    }
  }

  /// التحقق من المحاكيات
  Future<bool> _isRunningOnEmulator() async {
    try {
      if (Platform.isAndroid) {
        // فحص خصائص المحاكي
        // هنا يمكن إضافة فحص أكثر تفصيلاً
        // للخصائص المختلفة للجهاز مثل:
        // - Build.FINGERPRINT
        // - Build.MODEL
        // - Build.MANUFACTURER
        // - Build.BRAND
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في فحص المحاكي: $e');
      return false;
    }
  }

  /// حساب hash للتطبيق
  Future<String> _calculateAppHash() async {
    try {
      // حساب hash بسيط للتطبيق
      final appInfo = {
        'version': '1.0.0',
        'build': '1',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final jsonString = jsonEncode(appInfo);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      debugPrint('❌ خطأ في حساب hash التطبيق: $e');
      return 'fallback_hash';
    }
  }

  /// تسجيل محاولة تلاعب
  Future<void> _recordTamperAttempt(String reason) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_tamperDetectedKey, true);

      debugPrint('🚨 تم اكتشاف محاولة تلاعب: $reason');

      // يمكن إضافة إرسال تقرير للخادم هنا
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل محاولة التلاعب: $e');
    }
  }

  /// تحديث وقت آخر فحص
  Future<void> _updateLastCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCheckKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('❌ خطأ في تحديث وقت آخر فحص: $e');
    }
  }

  /// الحصول على وقت آخر فحص
  Future<DateTime?> getLastCheckTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckStr = prefs.getString(_lastCheckKey);

      if (lastCheckStr != null) {
        return DateTime.parse(lastCheckStr);
      }

      return null;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على وقت آخر فحص: $e');
      return null;
    }
  }

  /// فحص دوري للتكامل
  Future<void> performPeriodicCheck() async {
    try {
      final lastCheck = await getLastCheckTime();
      final now = DateTime.now();

      // فحص كل 24 ساعة
      if (lastCheck == null || now.difference(lastCheck).inHours >= 24) {
        await verifyIntegrity();
      }
    } catch (e) {
      debugPrint('❌ خطأ في الفحص الدوري: $e');
    }
  }
}
