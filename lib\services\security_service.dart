import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

/// خدمة الأمان الشاملة للتطبيق
class SecurityService {
  static SecurityService? _instance;
  static SecurityService get instance => _instance ??= SecurityService._();
  SecurityService._();

  // مفاتيح التشفير
  static const String _keyPrefix = 'edutrack_secure_';
  static const String _deviceIdKey = '${_keyPrefix}device_id';
  static const String _appSignatureKey = '${_keyPrefix}app_signature';
  static const String _sessionTokenKey = '${_keyPrefix}session_token';
  static const String _lastAccessKey = '${_keyPrefix}last_access';
  static const String _failedAttemptsKey = '${_keyPrefix}failed_attempts';
  static const String _lockoutTimeKey = '${_keyPrefix}lockout_time';

  // إعدادات الأمان
  static const int maxFailedAttempts = 5;
  static const int lockoutDurationMinutes = 30;
  static const int sessionTimeoutMinutes = 60;

  late final encrypt.Encrypter _encrypter;
  late final encrypt.IV _iv;
  String? _deviceId;
  String? _sessionToken;
  DateTime? _lastAccess;

  /// تهيئة خدمة الأمان
  Future<void> initialize() async {
    try {
      // إنشاء مفتاح التشفير
      final key = encrypt.Key.fromSecureRandom(32);
      _encrypter = encrypt.Encrypter(encrypt.AES(key));
      _iv = encrypt.IV.fromSecureRandom(16);

      // تحديد هوية الجهاز
      await _generateDeviceId();

      // التحقق من توقيع التطبيق
      await _verifyAppSignature();

      // إنشاء جلسة جديدة
      await _createSession();

      // تنظيف البيانات القديمة
      await _cleanupOldData();

      debugPrint('🔒 تم تهيئة نظام الأمان بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام الأمان: $e');
      rethrow;
    }
  }

  /// تشفير البيانات
  String encryptData(String data) {
    try {
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      debugPrint('❌ خطأ في تشفير البيانات: $e');
      return data; // إرجاع البيانات الأصلية في حالة الخطأ
    }
  }

  /// فك تشفير البيانات
  String decryptData(String encryptedData) {
    try {
      final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
      return _encrypter.decrypt(encrypted, iv: _iv);
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير البيانات: $e');
      return encryptedData; // إرجاع البيانات المشفرة في حالة الخطأ
    }
  }

  /// التحقق من صحة الجلسة
  Future<bool> validateSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedToken = prefs.getString(_sessionTokenKey);
      final lastAccessStr = prefs.getString(_lastAccessKey);

      if (storedToken == null || lastAccessStr == null) {
        return false;
      }

      final lastAccess = DateTime.parse(lastAccessStr);
      final now = DateTime.now();

      // التحقق من انتهاء صلاحية الجلسة
      if (now.difference(lastAccess).inMinutes > sessionTimeoutMinutes) {
        await _clearSession();
        return false;
      }

      // التحقق من صحة الرمز المميز
      if (storedToken != _sessionToken) {
        await _clearSession();
        return false;
      }

      // تحديث وقت آخر وصول
      await _updateLastAccess();
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الجلسة: $e');
      return false;
    }
  }

  /// التحقق من حالة القفل
  Future<bool> isLocked() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutTimeStr = prefs.getString(_lockoutTimeKey);

      if (lockoutTimeStr == null) return false;

      final lockoutTime = DateTime.parse(lockoutTimeStr);
      final now = DateTime.now();

      if (now.difference(lockoutTime).inMinutes < lockoutDurationMinutes) {
        return true;
      } else {
        // انتهت فترة القفل
        await _clearLockout();
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة القفل: $e');
      return false;
    }
  }

  /// تسجيل محاولة فاشلة
  Future<void> recordFailedAttempt() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentAttempts = prefs.getInt(_failedAttemptsKey) ?? 0;
      final newAttempts = currentAttempts + 1;

      await prefs.setInt(_failedAttemptsKey, newAttempts);

      if (newAttempts >= maxFailedAttempts) {
        await _lockApp();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }

  /// مسح المحاولات الفاشلة
  Future<void> clearFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_failedAttemptsKey);
    } catch (e) {
      debugPrint('❌ خطأ في مسح المحاولات الفاشلة: $e');
    }
  }

  /// الحصول على عدد المحاولات الفاشلة
  Future<int> getFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_failedAttemptsKey) ?? 0;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المحاولات الفاشلة: $e');
      return 0;
    }
  }

  /// الحصول على وقت القفل المتبقي
  Future<Duration?> getRemainingLockTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutTimeStr = prefs.getString(_lockoutTimeKey);

      if (lockoutTimeStr == null) return null;

      final lockoutTime = DateTime.parse(lockoutTimeStr);
      final now = DateTime.now();
      final elapsed = now.difference(lockoutTime);

      if (elapsed.inMinutes < lockoutDurationMinutes) {
        return Duration(minutes: lockoutDurationMinutes) - elapsed;
      }

      return null;
    } catch (e) {
      debugPrint('❌ خطأ في حساب وقت القفل المتبقي: $e');
      return null;
    }
  }

  /// تنظيف البيانات الحساسة
  Future<void> clearSensitiveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionTokenKey);
      await prefs.remove(_lastAccessKey);
      await prefs.remove(_failedAttemptsKey);
      await prefs.remove(_lockoutTimeKey);

      _sessionToken = null;
      _lastAccess = null;

      debugPrint('🧹 تم تنظيف البيانات الحساسة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات الحساسة: $e');
    }
  }

  /// إنشاء هوية الجهاز
  Future<void> _generateDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _deviceId = prefs.getString(_deviceIdKey);

      if (_deviceId == null) {
        final deviceInfo = DeviceInfoPlugin();
        String deviceIdentifier = '';

        if (defaultTargetPlatform == TargetPlatform.android) {
          final androidInfo = await deviceInfo.androidInfo;
          deviceIdentifier = '${androidInfo.model}_${androidInfo.id}';
        } else if (defaultTargetPlatform == TargetPlatform.iOS) {
          final iosInfo = await deviceInfo.iosInfo;
          deviceIdentifier = '${iosInfo.model}_${iosInfo.identifierForVendor}';
        } else {
          // للمنصات الأخرى
          deviceIdentifier = 'web_${DateTime.now().millisecondsSinceEpoch}';
        }

        // تشفير هوية الجهاز
        final hashedId = sha256
            .convert(utf8.encode(deviceIdentifier))
            .toString();
        _deviceId = hashedId.substring(0, 16);

        await prefs.setString(_deviceIdKey, _deviceId!);
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء هوية الجهاز: $e');
      _deviceId = 'fallback_${Random().nextInt(999999)}';
    }
  }

  /// التحقق من توقيع التطبيق
  Future<void> _verifyAppSignature() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      const expectedSignature = 'edutrack_v1.0_secure';
      final storedSignature = prefs.getString(_appSignatureKey);

      if (storedSignature != expectedSignature) {
        await prefs.setString(_appSignatureKey, expectedSignature);
        debugPrint('🔏 تم تحديث توقيع التطبيق');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من توقيع التطبيق: $e');
    }
  }

  /// إنشاء جلسة جديدة
  Future<void> _createSession() async {
    try {
      _sessionToken = _generateSecureToken();
      _lastAccess = DateTime.now();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_sessionTokenKey, _sessionToken!);
      await prefs.setString(_lastAccessKey, _lastAccess!.toIso8601String());
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الجلسة: $e');
    }
  }

  /// تحديث وقت آخر وصول
  Future<void> _updateLastAccess() async {
    try {
      _lastAccess = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastAccessKey, _lastAccess!.toIso8601String());
    } catch (e) {
      debugPrint('❌ خطأ في تحديث وقت آخر وصول: $e');
    }
  }

  /// مسح الجلسة
  Future<void> _clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionTokenKey);
      await prefs.remove(_lastAccessKey);

      _sessionToken = null;
      _lastAccess = null;
    } catch (e) {
      debugPrint('❌ خطأ في مسح الجلسة: $e');
    }
  }

  /// قفل التطبيق
  Future<void> _lockApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lockoutTimeKey, DateTime.now().toIso8601String());
      debugPrint('🔒 تم قفل التطبيق بسبب المحاولات الفاشلة');
    } catch (e) {
      debugPrint('❌ خطأ في قفل التطبيق: $e');
    }
  }

  /// مسح حالة القفل
  Future<void> _clearLockout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lockoutTimeKey);
      await prefs.remove(_failedAttemptsKey);
    } catch (e) {
      debugPrint('❌ خطأ في مسح حالة القفل: $e');
    }
  }

  /// تنظيف البيانات القديمة
  Future<void> _cleanupOldData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith('${_keyPrefix}temp_')) {
          // مسح البيانات المؤقتة القديمة
          await prefs.remove(key);
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات القديمة: $e');
    }
  }

  /// إنشاء رمز مميز آمن
  String _generateSecureToken() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// الحصول على هوية الجهاز
  String? get deviceId => _deviceId;

  /// الحصول على رمز الجلسة
  String? get sessionToken => _sessionToken;
}
