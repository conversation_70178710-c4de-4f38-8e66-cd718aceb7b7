import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:install_plugin/install_plugin.dart';

/// خدمة التحديث التلقائي للتطبيق
/// تتحقق من وجود إصدار جديد باستخدام Firebase Remote Config
class AppUpdateService {
  static final AppUpdateService _instance = AppUpdateService._internal();
  factory AppUpdateService() => _instance;
  AppUpdateService._internal();

  // Firebase Remote Config instance
  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  // Dio instance للتحميل
  final Dio _dio = Dio();

  /// تهيئة Firebase Remote Config
  Future<void> _initializeRemoteConfig() async {
    try {
      // إعداد Remote Config settings
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      // إعداد القيم الافتراضية
      await _remoteConfig.setDefaults({
        'latest_version': '1.0.0',
        'apk_url': '',
      });

      debugPrint('تم تهيئة Firebase Remote Config بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة Remote Config: $e');
      rethrow;
    }
  }

  /// التحقق من الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final ConnectivityResult connectivityResult = await Connectivity()
          .checkConnectivity();

      // التحقق من وجود اتصال بالإنترنت
      bool isConnected =
          connectivityResult == ConnectivityResult.mobile ||
          connectivityResult == ConnectivityResult.wifi ||
          connectivityResult == ConnectivityResult.ethernet;

      debugPrint(
        'حالة الاتصال بالإنترنت: ${isConnected ? "متصل" : "غير متصل"}',
      );
      return isConnected;
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال: $e');
      return false;
    }
  }

  /// جلب معلومات الإصدار الحالي للتطبيق
  Future<String> _getCurrentVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String currentVersion = packageInfo.version;
      debugPrint('الإصدار الحالي للتطبيق: $currentVersion');
      return currentVersion;
    } catch (e) {
      debugPrint('خطأ في جلب معلومات التطبيق: $e');
      return '1.0.0'; // قيمة افتراضية
    }
  }

  /// جلب معلومات الإصدار الجديد من Remote Config
  Future<Map<String, String>> _getRemoteVersionInfo() async {
    try {
      // جلب البيانات من Remote Config
      await _remoteConfig.fetchAndActivate();

      String latestVersion = _remoteConfig.getString('latest_version');
      String apkUrl = _remoteConfig.getString('apk_url');

      debugPrint('آخر إصدار متاح: $latestVersion');
      debugPrint('رابط التحميل: $apkUrl');

      return {'latest_version': latestVersion, 'apk_url': apkUrl};
    } catch (e) {
      debugPrint('خطأ في جلب معلومات الإصدار: $e');
      rethrow;
    }
  }

  /// مقارنة الإصدارات
  bool _isUpdateAvailable(String currentVersion, String latestVersion) {
    try {
      // تحويل الإصدارات إلى أرقام للمقارنة
      List<int> current = currentVersion.split('.').map(int.parse).toList();
      List<int> latest = latestVersion.split('.').map(int.parse).toList();

      // التأكد من أن كلا القائمتين لهما نفس الطول
      while (current.length < latest.length) {
        current.add(0);
      }
      while (latest.length < current.length) {
        latest.add(0);
      }

      // مقارنة الإصدارات
      for (int i = 0; i < current.length; i++) {
        if (latest[i] > current[i]) {
          debugPrint('يوجد تحديث متاح: $currentVersion -> $latestVersion');
          return true;
        } else if (latest[i] < current[i]) {
          return false;
        }
      }

      debugPrint('لا يوجد تحديث متاح');
      return false;
    } catch (e) {
      debugPrint('خطأ في مقارنة الإصدارات: $e');
      return false;
    }
  }

  /// تحميل وتثبيت التحديث
  Future<void> _downloadAndInstallUpdate(
    String apkUrl,
    BuildContext context,
  ) async {
    try {
      // التحقق من أن الرابط صحيح
      if (apkUrl.isEmpty) {
        throw 'رابط التحميل غير صحيح';
      }

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل التحديث...'),
            ],
          ),
        ),
      );

      // الحصول على مجلد التحميل المؤقت
      Directory tempDir = await getTemporaryDirectory();
      String savePath = '${tempDir.path}/app_update.apk';

      debugPrint('مسار حفظ الملف: $savePath');

      // تحميل ملف APK
      await _dio.download(
        apkUrl,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            double progress = received / total;
            debugPrint('تقدم التحميل: ${(progress * 100).toStringAsFixed(1)}%');
          }
        },
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      debugPrint('تم تحميل الملف بنجاح');

      // تثبيت التطبيق (Android فقط)
      if (Platform.isAndroid) {
        await InstallPlugin.installApk(savePath);
        debugPrint('تم بدء عملية التثبيت');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      debugPrint('خطأ في تحميل أو تثبيت التحديث: $e');

      // عرض رسالة خطأ
      if (context.mounted) {
        _showErrorDialog(
          context,
          'فشل في تحميل التحديث. حاول مرة أخرى لاحقاً.',
        );
      }
    }
  }

  /// عرض حوار التحديث
  void _showUpdateDialog(BuildContext context, String apkUrl) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          'تحديث متاح',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        content: const Text(
          'يوجد إصدار جديد من التطبيق. هل تريد تحديث التطبيق الآن؟',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              debugPrint('المستخدم اختار التحديث لاحقاً');
            },
            child: const Text('لاحقاً', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              debugPrint('المستخدم اختار التحديث الآن');
              _downloadAndInstallUpdate(apkUrl, context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('تحديث الآن'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'خطأ',
          style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// الدالة الرئيسية للتحقق من التحديثات
  /// يتم استدعاؤها من main() أو initState()
  Future<void> checkForUpdate(BuildContext context) async {
    try {
      debugPrint('بدء التحقق من التحديثات...');

      // 1. التحقق من الاتصال بالإنترنت
      bool isConnected = await _checkInternetConnection();
      if (!isConnected) {
        debugPrint('لا يوجد اتصال بالإنترنت - تم إلغاء التحقق من التحديثات');
        return;
      }

      // 2. تهيئة Remote Config
      await _initializeRemoteConfig();

      // 3. جلب الإصدار الحالي
      String currentVersion = await _getCurrentVersion();

      // 4. جلب معلومات الإصدار الجديد
      Map<String, String> remoteVersionInfo = await _getRemoteVersionInfo();
      String latestVersion = remoteVersionInfo['latest_version']!;
      String apkUrl = remoteVersionInfo['apk_url']!;

      // 5. مقارنة الإصدارات
      bool updateAvailable = _isUpdateAvailable(currentVersion, latestVersion);

      // 6. عرض حوار التحديث إذا كان متاحاً
      if (updateAvailable && context.mounted) {
        _showUpdateDialog(context, apkUrl);
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');

      // عرض رسالة خطأ للمستخدم (اختياري)
      if (context.mounted) {
        _showErrorDialog(context, 'فشل في التحقق من التحديثات');
      }
    }
  }
}
