import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'firebase_service.dart';

/// خدمة المصادقة المتقدمة باستخدام Firebase
class FirebaseAuthService {
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  
  /// تسجيل دخول المعلم
  Future<Map<String, dynamic>> signInTeacher(String email, String password) async {
    try {
      // التحقق من صحة البيانات
      if (email.isEmpty || password.isEmpty) {
        throw 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      }

      // تسجيل الدخول
      UserCredential? result = await _firebaseService.signInWithEmailAndPassword(email, password);
      
      if (result?.user == null) {
        throw 'فشل في تسجيل الدخول';
      }

      User user = result!.user!;
      
      // التحقق من نوع المستخدم (معلم)
      DocumentSnapshot userDoc = await _firebaseService.getDocument('teachers', user.uid);
      
      if (!userDoc.exists) {
        // إنشاء ملف المعلم إذا لم يكن موجوداً
        await _createTeacherProfile(user);
      }

      // تسجيل نشاط تسجيل الدخول
      await _firebaseService.logEvent('teacher_login', {
        'user_id': user.uid,
        'email': user.email,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      return {
        'success': true,
        'user': user,
        'message': 'تم تسجيل الدخول بنجاح',
      };
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Teacher sign in failed');
      return {
        'success': false,
        'message': _getErrorMessage(e.toString()),
      };
    }
  }

  /// إنشاء حساب معلم جديد
  Future<Map<String, dynamic>> createTeacherAccount({
    required String email,
    required String password,
    required String fullName,
    required String subject,
    required String school,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (email.isEmpty || password.isEmpty || fullName.isEmpty) {
        throw 'يرجى إدخال جميع البيانات المطلوبة';
      }

      if (password.length < 6) {
        throw 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      // إنشاء الحساب
      UserCredential? result = await _firebaseService.createUserWithEmailAndPassword(email, password);
      
      if (result?.user == null) {
        throw 'فشل في إنشاء الحساب';
      }

      User user = result!.user!;

      // تحديث ملف المستخدم
      await user.updateDisplayName(fullName);

      // إنشاء ملف المعلم في قاعدة البيانات
      await _createTeacherProfile(user, {
        'fullName': fullName,
        'subject': subject,
        'school': school,
      });

      // تسجيل نشاط إنشاء الحساب
      await _firebaseService.logEvent('teacher_signup', {
        'user_id': user.uid,
        'email': user.email,
        'subject': subject,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      return {
        'success': true,
        'user': user,
        'message': 'تم إنشاء الحساب بنجاح',
      };
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Teacher account creation failed');
      return {
        'success': false,
        'message': _getErrorMessage(e.toString()),
      };
    }
  }

  /// إنشاء ملف المعلم في قاعدة البيانات
  Future<void> _createTeacherProfile(User user, [Map<String, dynamic>? additionalData]) async {
    try {
      Map<String, dynamic> teacherData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName ?? '',
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'role': 'teacher',
        'settings': {
          'notifications': true,
          'darkMode': false,
          'language': 'ar',
        },
        'stats': {
          'totalStudents': 0,
          'totalClasses': 0,
          'totalAssignments': 0,
        },
      };

      // إضافة البيانات الإضافية إذا وجدت
      if (additionalData != null) {
        teacherData.addAll(additionalData);
      }

      await _firebaseService.saveDocument('teachers', user.uid, teacherData);
      
      // تعيين معرف المستخدم في Analytics و Crashlytics
      await _firebaseService.setUserId(user.uid);
      await _firebaseService.setUserProperty('user_type', 'teacher');
      
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Create teacher profile failed');
      rethrow;
    }
  }

  /// تحديث آخر وقت تسجيل دخول
  Future<void> updateLastLogin() async {
    try {
      User? user = _firebaseService.currentUser;
      if (user != null) {
        await _firebaseService.firestore
            .collection('teachers')
            .doc(user.uid)
            .update({'lastLoginAt': FieldValue.serverTimestamp()});
      }
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Update last login failed');
    }
  }

  /// إرسال رسالة إعادة تعيين كلمة المرور
  Future<Map<String, dynamic>> resetPassword(String email) async {
    try {
      if (email.isEmpty) {
        throw 'يرجى إدخال البريد الإلكتروني';
      }

      await _firebaseService.sendPasswordResetEmail(email);

      return {
        'success': true,
        'message': 'تم إرسال رسالة إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
      };
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Password reset failed');
      return {
        'success': false,
        'message': _getErrorMessage(e.toString()),
      };
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _firebaseService.signOut();
      await _firebaseService.logEvent('teacher_logout', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Sign out failed');
      rethrow;
    }
  }

  /// الحصول على بيانات المعلم الحالي
  Future<Map<String, dynamic>?> getCurrentTeacherData() async {
    try {
      User? user = _firebaseService.currentUser;
      if (user == null) return null;

      DocumentSnapshot doc = await _firebaseService.getDocument('teachers', user.uid);
      if (doc.exists) {
        return doc.data() as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Get teacher data failed');
      return null;
    }
  }

  /// تحديث بيانات المعلم
  Future<bool> updateTeacherData(Map<String, dynamic> data) async {
    try {
      User? user = _firebaseService.currentUser;
      if (user == null) return false;

      data['updatedAt'] = FieldValue.serverTimestamp();
      
      await _firebaseService.firestore
          .collection('teachers')
          .doc(user.uid)
          .update(data);

      return true;
    } catch (e) {
      _firebaseService.recordError(e, null, reason: 'Update teacher data failed');
      return false;
    }
  }

  /// تحويل رسائل الخطأ إلى العربية
  String _getErrorMessage(String error) {
    if (error.contains('user-not-found')) {
      return 'المستخدم غير موجود';
    } else if (error.contains('wrong-password')) {
      return 'كلمة المرور غير صحيحة';
    } else if (error.contains('email-already-in-use')) {
      return 'البريد الإلكتروني مستخدم بالفعل';
    } else if (error.contains('weak-password')) {
      return 'كلمة المرور ضعيفة';
    } else if (error.contains('invalid-email')) {
      return 'البريد الإلكتروني غير صحيح';
    } else if (error.contains('network-request-failed')) {
      return 'خطأ في الاتصال بالإنترنت';
    } else if (error.contains('too-many-requests')) {
      return 'تم تجاوز عدد المحاولات المسموح، حاول مرة أخرى لاحقاً';
    } else {
      return 'حدث خطأ غير متوقع، حاول مرة أخرى';
    }
  }

  /// التحقق من حالة تسجيل الدخول
  bool get isSignedIn => _firebaseService.isSignedIn;

  /// الحصول على المستخدم الحالي
  User? get currentUser => _firebaseService.currentUser;

  /// الاستماع لتغييرات حالة المصادقة
  Stream<User?> get authStateChanges => _firebaseService.authStateChanges;
}
