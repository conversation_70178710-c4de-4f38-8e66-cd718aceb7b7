import 'package:flutter/material.dart';
import '../services/app_update_service.dart';
import '../widgets/update_checker_widget.dart';

/// شاشة اختبار نظام التحديث
class UpdateTestScreen extends StatefulWidget {
  const UpdateTestScreen({super.key});

  @override
  State<UpdateTestScreen> createState() => _UpdateTestScreenState();
}

class _UpdateTestScreenState extends State<UpdateTestScreen> {
  bool _isChecking = false;
  String _statusMessage = 'اضغط على الزر للتحقق من التحديثات';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نظام التحديث'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات النظام',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text('• يتحقق النظام من Firebase Remote Config'),
                    const Text('• يقارن latest_version مع الإصدار الحالي'),
                    const Text('• يعرض تنبيه إذا كان هناك تحديث متاح'),
                    const Text('• يحمل ويثبت APK تلقائياً'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // حالة النظام
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _isChecking ? Icons.sync : Icons.info_outline,
                      size: 48,
                      color: _isChecking ? Colors.blue : Colors.grey,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _statusMessage,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // أزرار الاختبار
            ElevatedButton.icon(
              onPressed: _isChecking ? null : _testUpdateCheck,
              icon: _isChecking 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.system_update),
              label: Text(_isChecking ? 'جاري التحقق...' : 'التحقق من التحديثات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // زر التحقق باستخدام Widget
            const ManualUpdateButton(
              text: 'التحقق باستخدام Widget',
              icon: Icons.download,
              color: Colors.green,
            ),
            
            const SizedBox(height: 20),
            
            // تعليمات الاختبار
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تعليمات الاختبار:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. تأكد من إعداد Firebase Remote Config'),
                    const Text('2. ضع latest_version أكبر من الإصدار الحالي'),
                    const Text('3. ضع رابط APK صحيح في apk_url'),
                    const Text('4. تأكد من الاتصال بالإنترنت'),
                    const Text('5. اضغط على زر التحقق'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختبار التحقق من التحديثات
  Future<void> _testUpdateCheck() async {
    setState(() {
      _isChecking = true;
      _statusMessage = 'جاري التحقق من التحديثات...';
    });

    try {
      await AppUpdateService().checkForUpdate(context);
      
      setState(() {
        _statusMessage = 'تم التحقق بنجاح. إذا لم يظهر تنبيه، فلا يوجد تحديث متاح.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ: ${e.toString()}';
      });
      
      debugPrint('خطأ في اختبار التحديث: $e');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }
}
