import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../providers/app_provider.dart';
import '../models/group.dart';
import '../models/student.dart';
import '../models/lesson.dart';

/// نظام الذكاء الاصطناعي الذكي الجديد
class SmartAICore {
  static const String _apiKey =
      'AIzaSyBLfXhh1Ihtr0vQZMpqEUXbw6xPHw5CU_Y'; // ضع مفتاح API الصحيح هنا
  static GenerativeModel? _model;

  /// تهيئة النظام
  static void initialize() {
    try {
      _model = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
      debugPrint('🤖 تم تهيئة نظام الذكاء الاصطناعي الذكي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الذكاء الاصطناعي: $e');
    }
  }

  /// معالجة رسالة المستخدم بذكاء
  static Future<AIResponse> processMessage(
    String message,
    AppProvider provider,
  ) async {
    try {
      // تحليل نوع الرسالة
      final analysis = await _analyzeMessage(message);

      // بناء السياق
      final context = _buildContext(provider);

      // إنشاء البرومبت الذكي
      final prompt = _createSmartPrompt(message, analysis, context);

      // إرسال للذكاء الاصطناعي
      final response = await _sendToAI(prompt);

      // معالجة الاستجابة
      return _processResponse(response, analysis, provider);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الرسالة: $e');
      return AIResponse.error('عذراً، حدث خطأ في معالجة طلبك');
    }
  }

  /// تحليل نوع الرسالة
  static Future<MessageAnalysis> _analyzeMessage(String message) async {
    try {
      final analysisPrompt =
          '''
حلل هذه الرسالة وحدد نوعها:
"$message"

أجب بـ JSON فقط:
{
  "type": "action|question|chat|greeting",
  "intent": "add|delete|update|view|help|casual",
  "entity": "student|group|lesson|attendance|none",
  "confidence": 0.95,
  "action_required": true
}
''';

      final response = await _sendToAI(analysisPrompt);
      final analysis = _parseJSON(response);

      return MessageAnalysis.fromMap(analysis);
    } catch (e) {
      // تحليل احتياطي بسيط
      return MessageAnalysis.fallback(message);
    }
  }

  /// بناء سياق البيانات
  static Map<String, dynamic> _buildContext(AppProvider provider) {
    return {
      'stats': {
        'total_students': provider.totalStudents,
        'total_groups': provider.totalGroups,
        'total_lessons': provider.lessons.length,
      },
      'recent_groups': provider.groups
          .take(3)
          .map(
            (g) => {
              'id': g.id,
              'name': g.name,
              'subject': g.subject,
              'student_count': g.studentIds.length,
            },
          )
          .toList(),
      'recent_students': provider.students
          .take(3)
          .map((s) => {'id': s.id, 'name': s.name, 'group_id': s.groupId})
          .toList(),
    };
  }

  /// إنشاء البرومبت الذكي المتطور والمرن
  static String _createSmartPrompt(
    String message,
    MessageAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    if (analysis.actionRequired) {
      return '''
أنت مساعد ذكي متطور ومرن لنظام إدارة التعليم EduTrack.

🎯 مهمتك: تحليل طلب المستخدم وتنفيذ أي عملية مطلوبة بأقصى مرونة ممكنة.

رسالة المستخدم: "$message"
نوع الطلب: ${analysis.type}
الهدف: ${analysis.intent}
الكيان: ${analysis.entity}

البيانات الحالية في النظام:
${jsonEncode(_sanitizeContext(context))}

## قدراتك الكاملة:

### 1. عمليات البيانات:
- إضافة/تعديل/حذف الطلاب والمجموعات والدروس
- نقل الطلاب بين المجموعات
- تحديث حالات الحضور والدفع
- إنشاء تقارير مخصصة
- حساب الإحصائيات والمعدلات

### 2. عمليات JSON المتقدمة:
- إنشاء ملفات JSON مخصصة
- دمج وتقسيم ملفات JSON
- تحويل تنسيقات البيانات
- إنشاء نسخ احتياطية ذكية
- استيراد من مصادر متعددة

### 3. عمليات متعددة في نفس الوقت:
- تنفيذ عدة إجراءات متتالية
- معالجة مجموعات من البيانات
- تطبيق تغييرات شاملة
- تنفيذ سيناريوهات معقدة

### 4. عمليات ذكية:
- تحليل البيانات واستخراج الأنماط
- اقتراح تحسينات
- إنشاء تقارير تفاعلية
- حل المشاكل تلقائياً

## بنية ملف JSON الكاملة لنظام EduTrack:

### 1. بنية الطالب (Student):
{
  "id": "معرف فريد (timestamp)",
  "name": "اسم الطالب الكامل",
  "groupId": "معرف المجموعة التي ينتمي إليها",
  "isPresent": true/false,
  "monthlyPayment": رقم (الرسوم الشهرية),
  "hasPaid": true/false,
  "lastAttendance": "تاريخ آخر حضور بصيغة ISO"
}

### 2. بنية المجموعة (Group):
{
  "id": "معرف فريد (timestamp)",
  "name": "اسم المجموعة",
  "subject": "المادة الدراسية",
  "studentIds": ["قائمة معرفات الطلاب"]
}

### 3. بنية الدرس (Lesson):
{
  "id": "معرف فريد (timestamp)",
  "groupId": "معرف المجموعة",
  "dateTime": "تاريخ ووقت الدرس بصيغة ISO",
  "isCompleted": true/false,
  "attendedStudentIds": ["قائمة معرفات الطلاب الحاضرين"],
  "notes": "ملاحظات الدرس"
}

### 4. بنية ملف النسخة الاحتياطية الكامل:
{
  "students": [قائمة كائنات الطلاب],
  "groups": [قائمة كائنات المجموعات],
  "lessons": [قائمة كائنات الدروس],
  "timestamp": "تاريخ إنشاء النسخة الاحتياطية"
}

## تعليمات التنفيذ المتطورة:

### 🎯 المبادئ الأساسية:
1. **المرونة الكاملة**: يمكنك تنفيذ أي عملية يطلبها المستخدم
2. **العمليات المتعددة**: يمكنك تنفيذ عدة إجراءات في نفس الوقت
3. **الذكاء التكيفي**: اقترح حلول إبداعية للمشاكل المعقدة
4. **التحليل العميق**: فهم السياق والهدف الحقيقي من الطلب

### 🔧 قواعد التنفيذ:
- حلل الطلب بعمق واستخرج جميع المتطلبات
- نفذ العمليات بالترتيب المنطقي الصحيح
- تأكد من سلامة البيانات والروابط
- اقترح تحسينات إضافية عند الحاجة
- استخدم timestamps فريدة للمعرفات الجديدة

### 📋 تنسيق الاستجابة المرن:

للعمليات البسيطة:
{
  "response": "وصف ما تم تنفيذه",
  "action": {
    "type": "نوع العملية",
    "entity": "الكيان المستهدف",
    "data": {البيانات},
    "json_operation": {تفاصيل عملية JSON}
  }
}

للعمليات المتعددة:
{
  "response": "وصف شامل لجميع العمليات",
  "actions": [
    {
      "type": "العملية الأولى",
      "entity": "الكيان الأول",
      "data": {البيانات الأولى},
      "priority": 1
    },
    {
      "type": "العملية الثانية",
      "entity": "الكيان الثاني",
      "data": {البيانات الثانية},
      "priority": 2
    }
  ],
  "json_operations": [
    {
      "operation": "نوع العملية",
      "target": "الهدف",
      "changes": "التغييرات"
    }
  ],
  "suggestions": ["اقتراحات إضافية"],
  "analytics": {
    "operations_count": عدد العمليات,
    "affected_entities": ["الكيانات المتأثرة"],
    "estimated_time": "الوقت المتوقع"
  }
}

للعمليات التحليلية:
{
  "response": "نتائج التحليل",
  "analysis": {
    "summary": "ملخص التحليل",
    "insights": ["الاستنتاجات"],
    "recommendations": ["التوصيات"],
    "data_quality": "تقييم جودة البيانات"
  },
  "visualizations": [
    {
      "type": "نوع المخطط",
      "data": {بيانات المخطط},
      "title": "عنوان المخطط"
    }
  ]
}
''';
    } else {
      return '''
أنت مساعد ودود لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$message"

أجب بشكل ودود ومفيد بـ JSON فقط:
{
  "response": "رد ودود ومفيد"
}
''';
    }
  }

  /// تنظيف السياق من DateTime objects
  static Map<String, dynamic> _sanitizeContext(Map<String, dynamic> context) {
    final sanitized = <String, dynamic>{};

    for (final entry in context.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is DateTime) {
        sanitized[key] = value.toIso8601String();
      } else if (value is List) {
        sanitized[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _sanitizeContext(item);
          } else if (item is DateTime) {
            return item.toIso8601String();
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = _sanitizeContext(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /// إرسال للذكاء الاصطناعي
  static Future<String> _sendToAI(String prompt) async {
    if (_model == null) {
      throw Exception('النموذج غير مهيأ');
    }

    final content = [Content.text(prompt)];
    final response = await _model!.generateContent(content);

    return response.text ?? 'لا توجد استجابة';
  }

  /// معالجة الاستجابة المتطورة والمرنة
  static Future<AIResponse> _processResponse(
    String response,
    MessageAnalysis analysis,
    AppProvider provider,
  ) async {
    try {
      final data = _parseJSON(response);
      final responseText = data['response'] as String? ?? 'تم معالجة طلبك';
      final results = <String>[responseText];

      // معالجة العملية الواحدة
      if (data.containsKey('action')) {
        final actionResult = await _executeAction(data['action'], provider);
        if (actionResult.isNotEmpty) results.add(actionResult);

        // تنفيذ عمليات JSON
        if (data['action'].containsKey('json_operation')) {
          final jsonResult = await _executeJSONOperation(
            data['action']['json_operation'],
            data['action'],
            provider,
          );
          if (jsonResult.isNotEmpty) results.add(jsonResult);
        }
      }

      // معالجة العمليات المتعددة
      if (data.containsKey('actions')) {
        final actions = data['actions'] as List?;
        if (actions != null) {
          // ترتيب العمليات حسب الأولوية
          actions.sort((a, b) {
            final priorityA = a['priority'] as int? ?? 999;
            final priorityB = b['priority'] as int? ?? 999;
            return priorityA.compareTo(priorityB);
          });

          results.add('\n🔄 تنفيذ العمليات المتعددة:');

          for (int i = 0; i < actions.length; i++) {
            final action = actions[i];
            results.add('\n📋 العملية ${i + 1}:');

            final actionResult = await _executeAction(action, provider);
            if (actionResult.isNotEmpty) {
              results.add(actionResult);
            }
          }
        }
      }

      // معالجة عمليات JSON المتعددة
      if (data.containsKey('json_operations')) {
        final jsonOps = data['json_operations'] as List?;
        if (jsonOps != null) {
          results.add('\n📄 عمليات JSON:');

          for (final jsonOp in jsonOps) {
            final jsonResult = await _executeJSONOperation(
              jsonOp,
              data,
              provider,
            );
            if (jsonResult.isNotEmpty) {
              results.add(jsonResult);
            }
          }
        }
      }

      // معالجة التحليلات
      if (data.containsKey('analysis')) {
        final analysisResult = _processAnalysis(data['analysis']);
        if (analysisResult.isNotEmpty) {
          results.add(analysisResult);
        }
      }

      // معالجة الاقتراحات
      if (data.containsKey('suggestions')) {
        final suggestions = data['suggestions'] as List?;
        if (suggestions != null && suggestions.isNotEmpty) {
          results.add('\n💡 اقتراحات إضافية:');
          for (final suggestion in suggestions) {
            results.add('• $suggestion');
          }
        }
      }

      // معالجة الإحصائيات
      if (data.containsKey('analytics')) {
        final analytics = _processAnalytics(data['analytics']);
        if (analytics.isNotEmpty) {
          results.add(analytics);
        }
      }

      final fullResult = results.where((s) => s.isNotEmpty).join('\n');
      return AIResponse.success(fullResult, hasAction: true);
    } catch (e) {
      return AIResponse.error('عذراً، حدث خطأ في معالجة الطلب: $e');
    }
  }

  /// معالجة نتائج التحليل
  static String _processAnalysis(Map<String, dynamic> analysis) {
    final summary = analysis['summary'] as String? ?? '';
    final insights = analysis['insights'] as List? ?? [];
    final recommendations = analysis['recommendations'] as List? ?? [];
    final dataQuality = analysis['data_quality'] as String? ?? '';

    final result = <String>[];

    if (summary.isNotEmpty) {
      result.add('\n📊 ملخص التحليل:');
      result.add(summary);
    }

    if (insights.isNotEmpty) {
      result.add('\n🔍 الاستنتاجات:');
      for (final insight in insights) {
        result.add('• $insight');
      }
    }

    if (recommendations.isNotEmpty) {
      result.add('\n💡 التوصيات:');
      for (final recommendation in recommendations) {
        result.add('• $recommendation');
      }
    }

    if (dataQuality.isNotEmpty) {
      result.add('\n⭐ تقييم جودة البيانات: $dataQuality');
    }

    return result.join('\n');
  }

  /// معالجة الإحصائيات
  static String _processAnalytics(Map<String, dynamic> analytics) {
    final operationsCount = analytics['operations_count'] as int? ?? 0;
    final affectedEntities = analytics['affected_entities'] as List? ?? [];
    final estimatedTime = analytics['estimated_time'] as String? ?? '';

    final result = <String>[];

    result.add('\n📈 إحصائيات العملية:');

    if (operationsCount > 0) {
      result.add('• عدد العمليات: $operationsCount');
    }

    if (affectedEntities.isNotEmpty) {
      result.add('• الكيانات المتأثرة: ${affectedEntities.join(', ')}');
    }

    if (estimatedTime.isNotEmpty) {
      result.add('• الوقت المتوقع: $estimatedTime');
    }

    return result.join('\n');
  }

  /// تنفيذ عمليات JSON المباشرة
  static Future<String> _executeJSONOperation(
    Map<String, dynamic> jsonOp,
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final operation = jsonOp['operation'] as String?;
      final changes = jsonOp['changes'] as String?;

      switch (operation) {
        case 'create':
          return await _createJSONBackup(action, provider);
        case 'modify':
          return await _modifyJSONData(action, provider);
        case 'export':
          return await _exportJSONData(action, provider);
        default:
          return '📄 عملية JSON: $changes';
      }
    } catch (e) {
      return '❌ خطأ في عملية JSON: $e';
    }
  }

  /// إنشاء نسخة احتياطية JSON
  static Future<String> _createJSONBackup(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      // جمع البيانات الحالية
      final backupData = {
        'students': provider.students
            .map(
              (s) => {
                'id': s.id,
                'name': s.name,
                'groupId': s.groupId,
                'isPresent': s.isPresent,
                'monthlyPayment': s.monthlyPayment,
                'hasPaid': s.hasPaid,
                'lastAttendance': s.lastAttendance.toIso8601String(),
              },
            )
            .toList(),
        'groups': provider.groups
            .map(
              (g) => {
                'id': g.id,
                'name': g.name,
                'subject': g.subject,
                'studentIds': g.studentIds,
              },
            )
            .toList(),
        'lessons': provider.lessons
            .map(
              (l) => {
                'id': l.id,
                'groupId': l.groupId,
                'dateTime': l.dateTime.toIso8601String(),
                'isCompleted': l.isCompleted,
                'attendedStudentIds': l.attendedStudentIds,
                'notes': l.notes,
              },
            )
            .toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تحويل إلى JSON منسق
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      return '''
📄 تم إنشاء ملف JSON:

```json
${jsonString.length > 500 ? '${jsonString.substring(0, 500)}...' : jsonString}
```

📊 إحصائيات الملف:
• الطلاب: ${(backupData['students'] as List?)?.length ?? 0}
• المجموعات: ${(backupData['groups'] as List?)?.length ?? 0}
• الدروس: ${(backupData['lessons'] as List?)?.length ?? 0}
• حجم الملف: ${(jsonString.length / 1024).toStringAsFixed(1)} KB
''';
    } catch (e) {
      return '❌ فشل في إنشاء ملف JSON: $e';
    }
  }

  /// تعديل بيانات JSON
  static Future<String> _modifyJSONData(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final entity = action['entity'] as String?;
      final data = action['data'] as Map<String, dynamic>?;

      if (entity == null || data == null) {
        return '❌ بيانات غير مكتملة للتعديل';
      }

      // إنشاء JSON للكائن الجديد/المعدل
      Map<String, dynamic> jsonObject = {};

      switch (entity) {
        case 'student':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'name': data['name'] ?? 'طالب جديد',
            'groupId': data['groupId'] ?? '',
            'isPresent': data['isPresent'] ?? false,
            'monthlyPayment': data['monthlyPayment'] ?? 0.0,
            'hasPaid': data['hasPaid'] ?? false,
            'lastAttendance': DateTime.now().toIso8601String(),
          };
          break;
        case 'group':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'name': data['name'] ?? 'مجموعة جديدة',
            'subject': data['subject'] ?? 'عام',
            'studentIds': data['studentIds'] ?? [],
          };
          break;
        case 'lesson':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'groupId': data['groupId'] ?? '',
            'dateTime': data['dateTime'] ?? DateTime.now().toIso8601String(),
            'isCompleted': data['isCompleted'] ?? false,
            'attendedStudentIds': data['attendedStudentIds'] ?? [],
            'notes': data['notes'] ?? '',
          };
          break;
      }

      final jsonString = const JsonEncoder.withIndent('  ').convert(jsonObject);

      return '''
📝 تم تعديل بيانات JSON للـ$entity:

```json
$jsonString
```

✅ البيانات جاهزة للحفظ في النظام
''';
    } catch (e) {
      return '❌ فشل في تعديل بيانات JSON: $e';
    }
  }

  /// تصدير بيانات JSON
  static Future<String> _exportJSONData(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final entity = action['entity'] as String?;

      Map<String, dynamic> exportData = {};

      switch (entity) {
        case 'students':
          exportData = {
            'students': provider.students
                .map(
                  (s) => {
                    'id': s.id,
                    'name': s.name,
                    'groupId': s.groupId,
                    'isPresent': s.isPresent,
                    'monthlyPayment': s.monthlyPayment,
                    'hasPaid': s.hasPaid,
                    'lastAttendance': s.lastAttendance.toIso8601String(),
                  },
                )
                .toList(),
            'export_date': DateTime.now().toIso8601String(),
          };
          break;
        case 'groups':
          exportData = {
            'groups': provider.groups
                .map(
                  (g) => {
                    'id': g.id,
                    'name': g.name,
                    'subject': g.subject,
                    'studentIds': g.studentIds,
                  },
                )
                .toList(),
            'export_date': DateTime.now().toIso8601String(),
          };
          break;
        default:
          exportData = {
            'message': 'نوع البيانات غير مدعوم للتصدير',
            'supported_types': ['students', 'groups', 'lessons'],
          };
      }

      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);

      return '''
📤 تم تصدير بيانات $entity:

```json
${jsonString.length > 800 ? '${jsonString.substring(0, 800)}...' : jsonString}
```

💾 الملف جاهز للحفظ أو المشاركة
''';
    } catch (e) {
      return '❌ فشل في تصدير البيانات: $e';
    }
  }

  /// حفظ ملف JSON على الجهاز
  static Future<String> saveJSONToFile(
    Map<String, dynamic> data,
    String fileName,
  ) async {
    try {
      // الحصول على مجلد التحميلات
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.json');

      // تحويل البيانات إلى JSON منسق
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);

      // كتابة الملف
      await file.writeAsString(jsonString);

      return '''
💾 تم حفظ ملف JSON بنجاح:

📁 المسار: ${file.path}
📊 الحجم: ${(jsonString.length / 1024).toStringAsFixed(1)} KB
📅 التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}

✅ يمكنك الآن مشاركة الملف أو استخدامه كنسخة احتياطية
''';
    } catch (e) {
      return '❌ فشل في حفظ الملف: $e';
    }
  }

  /// قراءة ملف JSON من الجهاز
  static Future<String> loadJSONFromFile(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        return '❌ الملف غير موجود: $filePath';
      }

      final jsonString = await file.readAsString();
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      return '''
📂 تم تحميل ملف JSON بنجاح:

📁 المسار: $filePath
📊 الحجم: ${(jsonString.length / 1024).toStringAsFixed(1)} KB

📋 محتويات الملف:
• الطلاب: ${(data['students'] as List?)?.length ?? 0}
• المجموعات: ${(data['groups'] as List?)?.length ?? 0}
• الدروس: ${(data['lessons'] as List?)?.length ?? 0}

✅ البيانات جاهزة للاستيراد
''';
    } catch (e) {
      return '❌ فشل في قراءة الملف: $e';
    }
  }

  /// تحليل وإصلاح ملف JSON
  static Future<String> analyzeAndFixJSON(String jsonString) async {
    try {
      // محاولة تحليل JSON
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // فحص البنية
      final issues = <String>[];
      final fixes = <String>[];

      // فحص الطلاب
      if (data.containsKey('students')) {
        final students = data['students'] as List?;
        if (students != null) {
          for (int i = 0; i < students.length; i++) {
            final student = students[i] as Map<String, dynamic>?;
            if (student != null) {
              // فحص الحقول المطلوبة
              if (!student.containsKey('id')) {
                student['id'] = DateTime.now().millisecondsSinceEpoch
                    .toString();
                fixes.add('تم إضافة معرف للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('name')) {
                student['name'] = 'طالب ${i + 1}';
                fixes.add('تم إضافة اسم للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('groupId')) {
                student['groupId'] = '';
                fixes.add('تم إضافة معرف مجموعة للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('isPresent')) {
                student['isPresent'] = false;
                fixes.add('تم إضافة حالة حضور للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('monthlyPayment')) {
                student['monthlyPayment'] = 0.0;
                fixes.add('تم إضافة رسوم شهرية للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('hasPaid')) {
                student['hasPaid'] = false;
                fixes.add('تم إضافة حالة دفع للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('lastAttendance')) {
                student['lastAttendance'] = DateTime.now().toIso8601String();
                fixes.add('تم إضافة تاريخ آخر حضور للطالب رقم ${i + 1}');
              }
            }
          }
        }
      }

      // فحص المجموعات
      if (data.containsKey('groups')) {
        final groups = data['groups'] as List?;
        if (groups != null) {
          for (int i = 0; i < groups.length; i++) {
            final group = groups[i] as Map<String, dynamic>?;
            if (group != null) {
              if (!group.containsKey('id')) {
                group['id'] = DateTime.now().millisecondsSinceEpoch.toString();
                fixes.add('تم إضافة معرف للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('name')) {
                group['name'] = 'مجموعة ${i + 1}';
                fixes.add('تم إضافة اسم للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('subject')) {
                group['subject'] = 'عام';
                fixes.add('تم إضافة مادة للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('studentIds')) {
                group['studentIds'] = <String>[];
                fixes.add('تم إضافة قائمة طلاب للمجموعة رقم ${i + 1}');
              }
            }
          }
        }
      }

      // إنشاء JSON محسن
      final fixedJsonString = const JsonEncoder.withIndent('  ').convert(data);

      return '''
🔍 تحليل ملف JSON:

${issues.isEmpty ? '✅ لا توجد مشاكل في البنية' : '⚠️ تم العثور على ${issues.length} مشكلة'}

${fixes.isEmpty ? '' : '''
🔧 الإصلاحات المطبقة:
${fixes.map((fix) => '• $fix').join('\n')}
'''}

📄 JSON المحسن:
```json
${fixedJsonString.length > 1000 ? '${fixedJsonString.substring(0, 1000)}...' : fixedJsonString}
```

✅ الملف جاهز للاستخدام
''';
    } catch (e) {
      return '''
❌ خطأ في تحليل JSON:

🔍 الخطأ: $e

💡 اقتراحات للإصلاح:
• تأكد من صحة تنسيق JSON
• تحقق من وجود جميع الأقواس والفواصل
• تأكد من أن النصوص محاطة بعلامات اقتباس
• تحقق من عدم وجود فواصل إضافية

📝 مثال على البنية الصحيحة:
```json
{
  "students": [
    {
      "id": "123456789",
      "name": "اسم الطالب",
      "groupId": "معرف المجموعة"
    }
  ]
}
```
''';
    }
  }

  /// تنفيذ الإجراءات المرنة والمتطورة
  static Future<String> _executeAction(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final type = action['type'] as String?;
      final entity = action['entity'] as String?;
      final data = action['data'] as Map<String, dynamic>?;

      if (type == null) {
        return '❌ نوع العملية غير محدد';
      }

      // العمليات العامة التي تعمل على أي كيان
      switch (type.toLowerCase()) {
        case 'analyze':
          return await _analyzeData(entity, data, provider);
        case 'report':
          return await _generateReport(entity, data, provider);
        case 'bulk_operation':
          return await _executeBulkOperation(data ?? {}, provider);
        case 'transfer':
          return await _transferData(data ?? {}, provider);
        case 'calculate':
          return await _calculateMetrics(entity, data ?? {}, provider);
        case 'search':
          return await _searchData(entity, data ?? {}, provider);
        case 'validate':
          return await _validateData(entity, data ?? {}, provider);
        case 'optimize':
          return await _optimizeData(entity, data ?? {}, provider);
        case 'custom':
          return await _executeCustomAction(type, entity, data ?? {}, provider);
      }

      // العمليات الخاصة بكل كيان
      if (entity != null) {
        switch (entity.toLowerCase()) {
          case 'group':
          case 'groups':
            return await _handleGroupAction(type, data ?? {}, provider);
          case 'student':
          case 'students':
            return await _handleStudentAction(type, data ?? {}, provider);
          case 'lesson':
          case 'lessons':
            return await _handleLessonAction(type, data ?? {}, provider);
          case 'attendance':
            return await _handleAttendanceAction(type, data ?? {}, provider);
          case 'payment':
          case 'payments':
            return await _handlePaymentAction(type, data ?? {}, provider);
          case 'schedule':
            return await _handleScheduleAction(type, data ?? {}, provider);
          case 'data':
          case 'system':
            return await _handleSystemAction(type, data ?? {}, provider);
          default:
            // محاولة تنفيذ عملية مخصصة
            return await _executeCustomAction(
              type,
              entity,
              data ?? {},
              provider,
            );
        }
      }

      return '❌ لم أتمكن من تحديد نوع العملية المطلوبة';
    } catch (e) {
      return '❌ فشل في تنفيذ الإجراء: $e';
    }
  }

  /// تحليل البيانات
  static Future<String> _analyzeData(
    String? entity,
    Map<String, dynamic>? data,
    AppProvider provider,
  ) async {
    final results = <String>[];

    switch (entity?.toLowerCase()) {
      case 'students':
        final totalStudents = provider.students.length;
        final presentStudents = provider.students
            .where((s) => s.isPresent)
            .length;
        final paidStudents = provider.students.where((s) => s.hasPaid).length;

        results.add('📊 تحليل الطلاب:');
        results.add('• إجمالي الطلاب: $totalStudents');
        results.add(
          '• الطلاب الحاضرون: $presentStudents (${totalStudents > 0 ? (presentStudents / totalStudents * 100).toStringAsFixed(1) : 0}%)',
        );
        results.add(
          '• الطلاب الذين دفعوا: $paidStudents (${totalStudents > 0 ? (paidStudents / totalStudents * 100).toStringAsFixed(1) : 0}%)',
        );
        break;

      case 'groups':
        final totalGroups = provider.groups.length;
        final groupsWithStudents = provider.groups
            .where((g) => g.studentIds.isNotEmpty)
            .length;

        results.add('📊 تحليل المجموعات:');
        results.add('• إجمالي المجموعات: $totalGroups');
        results.add('• المجموعات النشطة: $groupsWithStudents');
        break;

      default:
        results.add('📊 تحليل شامل للنظام:');
        results.add('• الطلاب: ${provider.students.length}');
        results.add('• المجموعات: ${provider.groups.length}');
        results.add('• الدروس: ${provider.lessons.length}');
    }

    return results.join('\n');
  }

  /// إنشاء التقارير
  static Future<String> _generateReport(
    String? entity,
    Map<String, dynamic>? data,
    AppProvider provider,
  ) async {
    final reportType = data?['report_type'] as String? ?? 'summary';
    final results = <String>[];

    results.add(
      '📋 تقرير ${entity ?? 'شامل'} - ${DateTime.now().toString().substring(0, 16)}',
    );
    results.add('=' * 50);

    switch (reportType.toLowerCase()) {
      case 'attendance':
        final totalStudents = provider.students.length;
        final presentStudents = provider.students
            .where((s) => s.isPresent)
            .length;
        results.add('📊 تقرير الحضور:');
        results.add('• إجمالي الطلاب: $totalStudents');
        results.add('• الحاضرون: $presentStudents');
        results.add('• الغائبون: ${totalStudents - presentStudents}');
        break;
      case 'payment':
        final totalStudents = provider.students.length;
        final paidStudents = provider.students.where((s) => s.hasPaid).length;
        final totalRevenue = provider.students
            .where((s) => s.hasPaid)
            .fold(0.0, (sum, s) => sum + s.monthlyPayment);
        results.add('💰 تقرير المدفوعات:');
        results.add('• إجمالي الطلاب: $totalStudents');
        results.add('• الذين دفعوا: $paidStudents');
        results.add('• المتأخرون: ${totalStudents - paidStudents}');
        results.add(
          '• إجمالي الإيرادات: ${totalRevenue.toStringAsFixed(2)} ريال',
        );
        break;
      default:
        results.add('📊 تقرير شامل:');
        results.add('• الطلاب: ${provider.students.length}');
        results.add('• المجموعات: ${provider.groups.length}');
        results.add('• الدروس: ${provider.lessons.length}');
    }

    return results.join('\n');
  }

  /// العمليات المجمعة
  static Future<String> _executeBulkOperation(
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final operations = data['operations'] as List? ?? [];
    final results = <String>[];

    results.add('🔄 تنفيذ العمليات المجمعة:');

    for (int i = 0; i < operations.length; i++) {
      final operation = operations[i] as Map<String, dynamic>;
      results.add('\n📋 العملية ${i + 1}:');

      final result = await _executeAction(operation, provider);
      results.add(result);
    }

    return results.join('\n');
  }

  /// نقل البيانات
  static Future<String> _transferData(
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final fromEntity = data['from'] as String?;
    final toEntity = data['to'] as String?;
    final itemIds = data['items'] as List? ?? [];

    if (fromEntity == null || toEntity == null) {
      return '❌ بيانات النقل غير مكتملة';
    }

    // مثال: نقل طلاب من مجموعة إلى أخرى
    if (fromEntity == 'group' && toEntity == 'group') {
      final fromGroupId = data['from_id'] as String?;
      final toGroupId = data['to_id'] as String?;

      if (fromGroupId != null && toGroupId != null) {
        int transferredCount = 0;

        for (final student in provider.students) {
          if (student.groupId == fromGroupId &&
              (itemIds.isEmpty || itemIds.contains(student.id))) {
            student.groupId = toGroupId;
            transferredCount++;
          }
        }

        return '✅ تم نقل $transferredCount طالب من المجموعة $fromGroupId إلى $toGroupId';
      }
    }

    return '❌ نوع النقل غير مدعوم';
  }

  /// حساب المقاييس
  static Future<String> _calculateMetrics(
    String? entity,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final metricType = data['metric'] as String? ?? 'all';
    final results = <String>[];

    results.add('📈 المقاييس المحسوبة:');

    switch (metricType.toLowerCase()) {
      case 'attendance_rate':
        final totalStudents = provider.students.length;
        final presentStudents = provider.students
            .where((s) => s.isPresent)
            .length;
        final rate = totalStudents > 0
            ? (presentStudents / totalStudents * 100)
            : 0;
        results.add('• معدل الحضور: ${rate.toStringAsFixed(1)}%');
        break;

      case 'payment_rate':
        final totalStudents = provider.students.length;
        final paidStudents = provider.students.where((s) => s.hasPaid).length;
        final rate = totalStudents > 0
            ? (paidStudents / totalStudents * 100)
            : 0;
        results.add('• معدل الدفع: ${rate.toStringAsFixed(1)}%');
        break;

      case 'revenue':
        final totalRevenue = provider.students
            .where((s) => s.hasPaid)
            .fold(0.0, (sum, s) => sum + s.monthlyPayment);
        results.add(
          '• إجمالي الإيرادات: ${totalRevenue.toStringAsFixed(2)} ريال',
        );
        break;

      default:
        final totalStudents = provider.students.length;
        final presentStudents = provider.students
            .where((s) => s.isPresent)
            .length;
        final paidStudents = provider.students.where((s) => s.hasPaid).length;
        final totalRevenue = provider.students
            .where((s) => s.hasPaid)
            .fold(0.0, (sum, s) => sum + s.monthlyPayment);

        results.add(
          '• معدل الحضور: ${totalStudents > 0 ? (presentStudents / totalStudents * 100).toStringAsFixed(1) : 0}%',
        );
        results.add(
          '• معدل الدفع: ${totalStudents > 0 ? (paidStudents / totalStudents * 100).toStringAsFixed(1) : 0}%',
        );
        results.add(
          '• إجمالي الإيرادات: ${totalRevenue.toStringAsFixed(2)} ريال',
        );
    }

    return results.join('\n');
  }

  /// البحث في البيانات
  static Future<String> _searchData(
    String? entity,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final query = data['query'] as String? ?? '';
    final results = <String>[];

    if (query.isEmpty) {
      return '❌ لم يتم تحديد نص البحث';
    }

    results.add('🔍 نتائج البحث عن "$query":');

    switch (entity?.toLowerCase()) {
      case 'students':
        final foundStudents = provider.students
            .where((s) => s.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
        results.add('• تم العثور على ${foundStudents.length} طالب');
        for (final student in foundStudents.take(5)) {
          results.add('  - ${student.name} (${student.id})');
        }
        break;

      case 'groups':
        final foundGroups = provider.groups
            .where(
              (g) =>
                  g.name.toLowerCase().contains(query.toLowerCase()) ||
                  g.subject.toLowerCase().contains(query.toLowerCase()),
            )
            .toList();
        results.add('• تم العثور على ${foundGroups.length} مجموعة');
        for (final group in foundGroups.take(5)) {
          results.add('  - ${group.name} (${group.subject})');
        }
        break;

      default:
        // بحث شامل
        final foundStudents = provider.students
            .where((s) => s.name.toLowerCase().contains(query.toLowerCase()))
            .length;
        final foundGroups = provider.groups
            .where(
              (g) =>
                  g.name.toLowerCase().contains(query.toLowerCase()) ||
                  g.subject.toLowerCase().contains(query.toLowerCase()),
            )
            .length;

        results.add('• الطلاب: $foundStudents نتيجة');
        results.add('• المجموعات: $foundGroups نتيجة');
    }

    return results.join('\n');
  }

  /// التحقق من صحة البيانات
  static Future<String> _validateData(
    String? entity,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final results = <String>[];
    final issues = <String>[];

    results.add('✅ فحص صحة البيانات:');

    // فحص الطلاب
    for (final student in provider.students) {
      if (student.name.isEmpty) {
        issues.add('طالب بدون اسم: ${student.id}');
      }
      if (student.groupId.isNotEmpty &&
          !provider.groups.any((g) => g.id == student.groupId)) {
        issues.add('طالب في مجموعة غير موجودة: ${student.name}');
      }
    }

    // فحص المجموعات
    for (final group in provider.groups) {
      if (group.name.isEmpty) {
        issues.add('مجموعة بدون اسم: ${group.id}');
      }
      for (final studentId in group.studentIds) {
        if (!provider.students.any((s) => s.id == studentId)) {
          issues.add('مجموعة تحتوي على طالب غير موجود: ${group.name}');
        }
      }
    }

    if (issues.isEmpty) {
      results.add('• جميع البيانات صحيحة ✅');
    } else {
      results.add('• تم العثور على ${issues.length} مشكلة:');
      for (final issue in issues.take(10)) {
        results.add('  - $issue');
      }
    }

    return results.join('\n');
  }

  /// تحسين البيانات
  static Future<String> _optimizeData(
    String? entity,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    final results = <String>[];
    int optimizations = 0;

    results.add('🔧 تحسين البيانات:');

    // إزالة الطلاب من المجموعات المحذوفة
    for (final student in provider.students) {
      if (student.groupId.isNotEmpty &&
          !provider.groups.any((g) => g.id == student.groupId)) {
        student.groupId = '';
        optimizations++;
      }
    }

    // تحديث قوائم الطلاب في المجموعات
    for (final group in provider.groups) {
      final validStudentIds = group.studentIds
          .where((id) => provider.students.any((s) => s.id == id))
          .toList();

      if (validStudentIds.length != group.studentIds.length) {
        group.studentIds.clear();
        group.studentIds.addAll(validStudentIds);
        optimizations++;
      }
    }

    results.add('• تم تطبيق $optimizations تحسين');
    results.add('• البيانات محسنة ومنظمة ✅');

    return results.join('\n');
  }

  /// تنفيذ عمليات مخصصة
  static Future<String> _executeCustomAction(
    String? type,
    String? entity,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    // يمكن للذكاء الاصطناعي تنفيذ أي عملية مخصصة هنا
    final customType = data['custom_type'] as String? ?? type;
    final customData = data['custom_data'] as Map<String, dynamic>? ?? {};

    return '''
🎯 تنفيذ عملية مخصصة:
• النوع: $customType
• الكيان: $entity
• البيانات: ${customData.keys.join(', ')}

✅ تم تنفيذ العملية المخصصة بنجاح
💡 يمكن للذكاء الاصطناعي تنفيذ أي عملية تطلبها
''';
  }

  /// معالجة إجراءات الحضور
  static Future<String> _handleAttendanceAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'mark_present':
        final studentId = data['student_id'] as String?;
        if (studentId != null) {
          final student = provider.students.firstWhere(
            (s) => s.id == studentId,
          );
          student.isPresent = true;
          student.lastAttendance = DateTime.now();
          return '✅ تم تسجيل حضور ${student.name}';
        }
        break;
      case 'mark_absent':
        final studentId = data['student_id'] as String?;
        if (studentId != null) {
          final student = provider.students.firstWhere(
            (s) => s.id == studentId,
          );
          student.isPresent = false;
          return '✅ تم تسجيل غياب ${student.name}';
        }
        break;
      case 'bulk_attendance':
        final attendanceList = data['attendance'] as List? ?? [];
        int marked = 0;
        for (final attendance in attendanceList) {
          final studentId = attendance['student_id'] as String?;
          final isPresent = attendance['is_present'] as bool? ?? false;
          if (studentId != null) {
            final student = provider.students.firstWhere(
              (s) => s.id == studentId,
            );
            student.isPresent = isPresent;
            if (isPresent) student.lastAttendance = DateTime.now();
            marked++;
          }
        }
        return '✅ تم تسجيل حضور/غياب $marked طالب';
    }
    return '❌ عملية حضور غير مدعومة';
  }

  /// معالجة إجراءات المدفوعات
  static Future<String> _handlePaymentAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'mark_paid':
        final studentId = data['student_id'] as String?;
        if (studentId != null) {
          final student = provider.students.firstWhere(
            (s) => s.id == studentId,
          );
          student.hasPaid = true;
          return '✅ تم تسجيل دفع ${student.name}';
        }
        break;
      case 'set_amount':
        final studentId = data['student_id'] as String?;
        final amount = data['amount'] as double?;
        if (studentId != null && amount != null) {
          final student = provider.students.firstWhere(
            (s) => s.id == studentId,
          );
          student.monthlyPayment = amount;
          return '✅ تم تحديد رسوم ${student.name} إلى $amount ريال';
        }
        break;
    }
    return '❌ عملية دفع غير مدعومة';
  }

  /// معالجة إجراءات الجدولة
  static Future<String> _handleScheduleAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'add_lesson':
        final groupId = data['group_id'] as String?;
        final dateTime = data['date_time'] as String?;
        if (groupId != null && dateTime != null) {
          final lesson = Lesson(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            groupId: groupId,
            dateTime: DateTime.parse(dateTime),
            isCompleted: false,
          );
          await provider.addLesson(lesson);
          return '✅ تم جدولة درس جديد';
        }
        break;
    }
    return '❌ عملية جدولة غير مدعومة';
  }

  /// معالجة إجراءات النظام
  static Future<String> _handleSystemAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'backup':
        return await _createJSONBackup({}, provider);
      case 'reset':
        // إعادة تعيين البيانات (بحذر)
        return '⚠️ عملية إعادة التعيين تتطلب تأكيد إضافي';
      case 'export':
        return await _exportJSONData({'entity': 'all'}, provider);
    }
    return '❌ عملية نظام غير مدعومة';
  }

  /// معالجة إجراءات المجموعات
  static Future<String> _handleGroupAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final group = Group(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'مجموعة جديدة',
          subject: data['subject'] as String? ?? 'عام',
          studentIds: [],
        );
        await provider.addGroup(group);
        return '✅ تم إضافة مجموعة "${group.name}" بنجاح';

      case 'delete':
        final groupId = data['id'] as String?;
        if (groupId != null) {
          await provider.deleteGroup(groupId);
          return '✅ تم حذف المجموعة بنجاح';
        }
        return '❌ معرف المجموعة مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للمجموعات';
    }
  }

  /// معالجة إجراءات الطلاب
  static Future<String> _handleStudentAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final student = Student(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'طالب جديد',
          groupId: data['groupId'] as String? ?? '',
        );
        await provider.addStudent(student);
        return '✅ تم إضافة طالب "${student.name}" بنجاح';

      case 'delete':
        final studentId = data['id'] as String?;
        if (studentId != null) {
          await provider.deleteStudent(studentId);
          return '✅ تم حذف الطالب بنجاح';
        }
        return '❌ معرف الطالب مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للطلاب';
    }
  }

  /// معالجة إجراءات الدروس
  static Future<String> _handleLessonAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final lesson = Lesson(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          groupId: data['groupId'] as String? ?? '',
          dateTime: DateTime.now(),
          isCompleted: false,
        );
        await provider.addLesson(lesson);
        return '✅ تم إضافة درس جديد بنجاح';

      default:
        return '❌ إجراء غير مدعوم للدروس';
    }
  }

  /// تحليل JSON مع معالجة الأخطاء
  static Map<String, dynamic> _parseJSON(String text) {
    try {
      // تنظيف النص من markdown
      String cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.substring(7);
      }
      if (cleanText.startsWith('```')) {
        cleanText = cleanText.substring(3);
      }
      if (cleanText.endsWith('```')) {
        cleanText = cleanText.substring(0, cleanText.length - 3);
      }

      // البحث عن JSON
      final jsonStart = cleanText.indexOf('{');
      final jsonEnd = cleanText.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = cleanText.substring(jsonStart, jsonEnd);
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }

      // إذا لم يوجد JSON، إنشاء استجابة افتراضية
      return {'response': cleanText};
    } catch (e) {
      return {'response': text};
    }
  }
}

/// تحليل الرسالة
class MessageAnalysis {
  final String type;
  final String intent;
  final String entity;
  final double confidence;
  final bool actionRequired;

  MessageAnalysis({
    required this.type,
    required this.intent,
    required this.entity,
    required this.confidence,
    required this.actionRequired,
  });

  factory MessageAnalysis.fromMap(Map<String, dynamic> map) {
    return MessageAnalysis(
      type: map['type'] as String? ?? 'chat',
      intent: map['intent'] as String? ?? 'casual',
      entity: map['entity'] as String? ?? 'none',
      confidence: (map['confidence'] as num?)?.toDouble() ?? 0.5,
      actionRequired: map['action_required'] as bool? ?? false,
    );
  }

  factory MessageAnalysis.fallback(String message) {
    final lowerMessage = message.toLowerCase();

    // تحليل بسيط للكلمات المفتاحية
    bool hasAction =
        lowerMessage.contains('أضف') ||
        lowerMessage.contains('احذف') ||
        lowerMessage.contains('أنشئ');

    String entity = 'none';
    if (lowerMessage.contains('مجموعة')) entity = 'group';
    if (lowerMessage.contains('طالب')) entity = 'student';
    if (lowerMessage.contains('درس')) entity = 'lesson';

    return MessageAnalysis(
      type: hasAction ? 'action' : 'chat',
      intent: hasAction ? 'add' : 'casual',
      entity: entity,
      confidence: 0.7,
      actionRequired: hasAction,
    );
  }
}

/// استجابة الذكاء الاصطناعي
class AIResponse {
  final String message;
  final bool isSuccess;
  final bool hasAction;
  final String? error;

  AIResponse({
    required this.message,
    required this.isSuccess,
    this.hasAction = false,
    this.error,
  });

  factory AIResponse.success(String message, {bool hasAction = false}) {
    return AIResponse(message: message, isSuccess: true, hasAction: hasAction);
  }

  factory AIResponse.error(String error) {
    return AIResponse(message: error, isSuccess: false, error: error);
  }
}
