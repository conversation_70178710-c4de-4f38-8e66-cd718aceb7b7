import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../services/security_service.dart';
import '../services/integrity_service.dart';

import '../theme/app_theme.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({super.key});

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = false;
  bool _integrityStatus = false;
  bool _sessionValid = false;
  int _failedAttempts = 0;
  Duration? _lockoutTime;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _loadSecurityStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSecurityStatus() async {
    setState(() => _isLoading = true);

    try {
      // التحقق من تكامل التطبيق
      _integrityStatus = await IntegrityService.instance.verifyIntegrity();

      // التحقق من صحة الجلسة
      _sessionValid = await SecurityService.instance.validateSession();

      // الحصول على عدد المحاولات الفاشلة
      _failedAttempts = await SecurityService.instance.getFailedAttempts();

      // الحصول على وقت القفل المتبقي
      _lockoutTime = await SecurityService.instance.getRemainingLockTime();
    } catch (e) {
      debugPrint('خطأ في تحميل حالة الأمان: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: AppTheme.getBackgroundColor(provider.isDarkMode),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(provider),
                  const SizedBox(height: 24),
                  _buildSecurityStatus(provider),
                  const SizedBox(height: 20),
                  _buildSecurityActions(provider),
                  const SizedBox(height: 20),
                  _buildAdvancedSettings(provider),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppTheme.getCardGradient(provider.isDarkMode),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.security, color: Colors.white, size: 32),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الأمان والحماية',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.getTextPrimaryColor(provider.isDarkMode),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة إعدادات الأمان وحماية البيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: AppTheme.getTextMutedColor(provider.isDarkMode),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityStatus(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.getCardGradient(provider.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.shield_outlined, color: AppTheme.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'حالة الأمان',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextPrimaryColor(provider.isDarkMode),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else
            Column(
              children: [
                _buildStatusItem(
                  'تكامل التطبيق',
                  _integrityStatus,
                  Icons.verified_user,
                  provider,
                ),
                const SizedBox(height: 12),
                _buildStatusItem(
                  'صحة الجلسة',
                  _sessionValid,
                  Icons.access_time,
                  provider,
                ),
                const SizedBox(height: 12),
                _buildFailedAttemptsStatus(provider),
                if (_lockoutTime != null) ...[
                  const SizedBox(height: 12),
                  _buildLockoutStatus(provider),
                ],
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(
    String title,
    bool status,
    IconData icon,
    AppProvider provider,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: status ? AppTheme.success : AppTheme.danger,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppTheme.getTextSecondaryColor(provider.isDarkMode),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: status ? AppTheme.success : AppTheme.danger,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            status ? 'آمن' : 'تحذير',
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFailedAttemptsStatus(AppProvider provider) {
    final color = _failedAttempts == 0
        ? AppTheme.success
        : _failedAttempts < 3
        ? AppTheme.warning
        : AppTheme.danger;

    return Row(
      children: [
        Icon(Icons.warning_outlined, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'المحاولات الفاشلة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppTheme.getTextSecondaryColor(provider.isDarkMode),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            '$_failedAttempts/5',
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLockoutStatus(AppProvider provider) {
    final minutes = _lockoutTime!.inMinutes;
    final seconds = _lockoutTime!.inSeconds % 60;

    return Row(
      children: [
        const Icon(Icons.lock_clock, color: AppTheme.danger, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'التطبيق مقفل',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppTheme.getTextSecondaryColor(provider.isDarkMode),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.danger,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            '$minutes:${seconds.toString().padLeft(2, '0')}',
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityActions(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.getCardGradient(provider.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.build_outlined, color: AppTheme.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'إجراءات الأمان',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextPrimaryColor(provider.isDarkMode),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildActionButton(
            'فحص التكامل',
            'التحقق من تكامل التطبيق والبيانات',
            Icons.security_update_good,
            () => _performIntegrityCheck(),
            provider,
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            'تجديد الجلسة',
            'إنشاء جلسة أمان جديدة',
            Icons.refresh,
            () => _renewSession(),
            provider,
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            'مسح المحاولات الفاشلة',
            'إعادة تعيين عداد المحاولات الفاشلة',
            Icons.clear_all,
            () => _clearFailedAttempts(),
            provider,
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            'تنظيف البيانات الحساسة',
            'مسح البيانات المؤقتة والحساسة',
            Icons.cleaning_services,
            () => _cleanSensitiveData(),
            provider,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    AppProvider provider,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.getSurfaceColor(
            provider.isDarkMode,
          ).withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppTheme.primary, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getTextPrimaryColor(provider.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppTheme.getTextMutedColor(provider.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.getTextMutedColor(provider.isDarkMode),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.getCardGradient(provider.isDarkMode),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings_outlined, color: AppTheme.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'الإعدادات المتقدمة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextPrimaryColor(provider.isDarkMode),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'معرف الجهاز: ${SecurityService.instance.deviceId ?? 'غير محدد'}',
            style: GoogleFonts.robotoMono(
              fontSize: 12,
              color: AppTheme.getTextMutedColor(provider.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'رمز الجلسة: ${SecurityService.instance.sessionToken?.substring(0, 16) ?? 'غير محدد'}...',
            style: GoogleFonts.robotoMono(
              fontSize: 12,
              color: AppTheme.getTextMutedColor(provider.isDarkMode),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _performIntegrityCheck() async {
    setState(() => _isLoading = true);

    try {
      final result = await IntegrityService.instance.verifyIntegrity();
      setState(() => _integrityStatus = result);

      _showResultDialog(
        result ? 'نجح فحص التكامل' : 'فشل فحص التكامل',
        result ? 'التطبيق آمن وسليم' : 'تم اكتشاف مشكلة في التكامل',
        result,
      );
    } catch (e) {
      _showResultDialog('خطأ', 'حدث خطأ أثناء فحص التكامل', false);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _renewSession() async {
    try {
      await SecurityService.instance.initialize();
      setState(() => _sessionValid = true);
      _showResultDialog('تم التجديد', 'تم تجديد الجلسة بنجاح', true);
    } catch (e) {
      _showResultDialog('خطأ', 'فشل في تجديد الجلسة', false);
    }
  }

  Future<void> _clearFailedAttempts() async {
    try {
      await SecurityService.instance.clearFailedAttempts();
      setState(() {
        _failedAttempts = 0;
        _lockoutTime = null;
      });
      _showResultDialog('تم المسح', 'تم مسح المحاولات الفاشلة', true);
    } catch (e) {
      _showResultDialog('خطأ', 'فشل في مسح المحاولات الفاشلة', false);
    }
  }

  Future<void> _cleanSensitiveData() async {
    try {
      await SecurityService.instance.clearSensitiveData();
      _showResultDialog('تم التنظيف', 'تم تنظيف البيانات الحساسة', true);
    } catch (e) {
      _showResultDialog('خطأ', 'فشل في تنظيف البيانات', false);
    }
  }

  void _showResultDialog(String title, String message, bool success) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.getCardColor(
          context.read<AppProvider>().isDarkMode,
        ),
        title: Row(
          children: [
            Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? AppTheme.success : AppTheme.danger,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                color: AppTheme.getTextPrimaryColor(
                  context.read<AppProvider>().isDarkMode,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: GoogleFonts.cairo(
            color: AppTheme.getTextSecondaryColor(
              context.read<AppProvider>().isDarkMode,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'موافق',
              style: GoogleFonts.cairo(color: AppTheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}
