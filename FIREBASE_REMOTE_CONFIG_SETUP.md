# إعداد Firebase Remote Config لنظام التحديث التلقائي

## الخطوات المطلوبة في Firebase Console:

### 1. تفعيل Remote Config
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك `edu-track-95f66`
3. من القائمة الجانبية، اختر **Remote Config**
4. انقر على **Create configuration**

### 2. إضافة المتغيرات المطلوبة

#### المتغير الأول: latest_version
- **Parameter key**: `latest_version`
- **Default value**: `1.0.0`
- **Description**: رقم آخر إصدار من التطبيق
- **Data type**: String

#### المتغير الثاني: apk_url
- **Parameter key**: `apk_url`
- **Default value**: `https://example.com/app-release.apk`
- **Description**: رابط مباشر لتحميل ملف APK
- **Data type**: String

### 3. نشر الإعدادات
1. بعد إضافة المتغيرات، انقر على **Publish changes**
2. أضف وصف للتغيير (مثل: "إعداد نظام التحديث التلقائي")
3. انقر على **Publish**

## كيفية تحديث الإصدار:

### عند إصدار نسخة جديدة:
1. ارفع ملف APK الجديد إلى خادم أو Firebase Storage
2. احصل على الرابط المباشر للملف
3. في Firebase Console > Remote Config:
   - حدث `latest_version` إلى رقم الإصدار الجديد (مثل: `1.0.1`)
   - حدث `apk_url` إلى الرابط الجديد
4. انقر على **Publish changes**

## مثال على القيم:

```json
{
  "latest_version": "1.0.1",
  "apk_url": "https://firebasestorage.googleapis.com/v0/b/edu-track-95f66.appspot.com/o/releases%2Fapp-release-v1.0.1.apk?alt=media&token=your-token-here"
}
```

## ملاحظات مهمة:

### 1. رفع ملف APK:
- يمكن استخدام Firebase Storage لرفع ملف APK
- أو أي خدمة استضافة أخرى (Google Drive, Dropbox, إلخ)
- تأكد من أن الرابط مباشر وقابل للتحميل

### 2. أرقام الإصدارات:
- استخدم نظام الترقيم المعياري (Semantic Versioning)
- مثال: `1.0.0`, `1.0.1`, `1.1.0`, `2.0.0`
- النظام يقارن الأرقام تلقائياً

### 3. الأمان:
- تأكد من أن ملف APK موقع رقمياً
- استخدم HTTPS للروابط
- تحقق من صحة الملف قبل التثبيت

## اختبار النظام:

### 1. اختبار محلي:
```dart
// في أي مكان في التطبيق
await AppUpdateService().checkForUpdate(context);
```

### 2. اختبار مع إصدار وهمي:
1. في Remote Config، ضع `latest_version` أكبر من الإصدار الحالي
2. ضع رابط APK صحيح
3. شغل التطبيق وتحقق من ظهور التنبيه

### 3. اختبار بدون إنترنت:
- افصل الإنترنت وشغل التطبيق
- يجب ألا يظهر أي تنبيه أو خطأ

## استكشاف الأخطاء:

### المشاكل الشائعة:
1. **لا يظهر تنبيه التحديث**: تحقق من أن `latest_version` أكبر من الإصدار الحالي
2. **فشل التحميل**: تحقق من صحة رابط APK
3. **فشل التثبيت**: تحقق من الأذونات في AndroidManifest.xml
4. **خطأ في Remote Config**: تحقق من إعدادات Firebase

### سجلات التشخيص:
- تحقق من سجلات Flutter في وحدة التحكم
- ابحث عن رسائل تبدأ بـ "تم تهيئة Firebase Remote Config"
- تحقق من رسائل الخطأ في حالة فشل العملية

## الاستخدام في الإنتاج:

### 1. في StartupScreen (تلقائي):
```dart
// يتم التحقق تلقائياً عند بدء التطبيق
// لا حاجة لكود إضافي
```

### 2. في أي شاشة أخرى:
```dart
// استخدام Widget
UpdateCheckerWidget(
  checkOnInit: true,
  child: YourScreen(),
)

// أو استدعاء مباشر
await AppUpdateService().checkForUpdate(context);
```

### 3. زر التحقق اليدوي:
```dart
ManualUpdateButton(
  text: 'تحقق من التحديثات',
  icon: Icons.download,
  color: Colors.blue,
)
```
